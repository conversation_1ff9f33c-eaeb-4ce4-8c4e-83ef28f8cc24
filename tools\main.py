#!/usr/bin/env python3
"""
Main CLI Tool for WhatsApp Zero-Day Exploit Simulation
Educational demonstration and testing interface

⚠️ EDUCATIONAL PURPOSE ONLY ⚠️
"""

import sys
import argparse
import logging
from typing import Dict, List, Optional
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text

# Import our modules
from exploits import FileExploit, ImageExploit, VoiceExploit
from payloads import PayloadManager, StealthManager, PersistenceManager
from tools.generator.exploit_generator import ExploitGenerator
from tools.tester.exploit_tester import ExploitTester
from tools.analyzer.exploit_analyzer import ExploitAnalyzer

console = Console()

def print_banner():
    """Print the application banner"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    WhatsApp Zero-Day Exploit Simulation                     ║
║                           Educational Purpose Only                          ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    console.print(banner, style="bold blue")
    
    disclaimer = Panel(
        "[bold red]⚠️  EDUCATIONAL PURPOSE ONLY ⚠️[/bold red]\n\n"
        "This tool is for educational and research purposes only.\n"
        "Using these techniques for malicious purposes is illegal and unethical.\n"
        "Always follow responsible disclosure practices.",
        title="[bold red]DISCLAIMER[/bold red]",
        border_style="red"
    )
    console.print(disclaimer)

def create_parser() -> argparse.ArgumentParser:
    """Create the command line argument parser"""
    parser = argparse.ArgumentParser(
        description="WhatsApp Zero-Day Exploit Simulation Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s generate --type file --output malicious.pdf
  %(prog)s test --file malicious.pdf --analysis-only
  %(prog)s analyze --file suspicious.jpg --detailed
  %(prog)s demo --scenario voice-call
        """
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug logging'
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Generate command
    gen_parser = subparsers.add_parser('generate', help='Generate exploit samples')
    gen_parser.add_argument('--type', choices=['file', 'image', 'voice'], required=True,
                           help='Type of exploit to generate')
    gen_parser.add_argument('--subtype', help='Specific subtype (pdf, jpeg, codec, etc.)')
    gen_parser.add_argument('--output', required=True, help='Output file path')
    gen_parser.add_argument('--payload', help='Custom payload file')
    
    # Test command
    test_parser = subparsers.add_parser('test', help='Test exploit samples')
    test_parser.add_argument('--file', required=True, help='File to test')
    test_parser.add_argument('--analysis-only', action='store_true',
                            help='Only analyze, do not execute')
    test_parser.add_argument('--safe-mode', action='store_true', default=True,
                            help='Run in safe mode (default)')
    
    # Analyze command
    analyze_parser = subparsers.add_parser('analyze', help='Analyze files for exploits')
    analyze_parser.add_argument('--file', required=True, help='File to analyze')
    analyze_parser.add_argument('--detailed', action='store_true',
                               help='Detailed analysis report')
    analyze_parser.add_argument('--output-format', choices=['text', 'json', 'html'],
                               default='text', help='Output format')
    
    # Demo command
    demo_parser = subparsers.add_parser('demo', help='Run demonstration scenarios')
    demo_parser.add_argument('--scenario', 
                            choices=['file-exploit', 'image-exploit', 'voice-call', 'full-attack'],
                            required=True, help='Demonstration scenario')
    demo_parser.add_argument('--interactive', action='store_true',
                            help='Interactive demonstration mode')
    
    # Payload command
    payload_parser = subparsers.add_parser('payload', help='Payload management')
    payload_parser.add_argument('--action', 
                               choices=['create', 'execute', 'list', 'remove'],
                               required=True, help='Payload action')
    payload_parser.add_argument('--type', help='Payload type')
    payload_parser.add_argument('--id', help='Payload ID')
    payload_parser.add_argument('--code', help='Payload code file')
    
    return parser

def handle_generate_command(args) -> int:
    """Handle the generate command"""
    console.print(f"[bold green]Generating {args.type} exploit...[/bold green]")
    
    try:
        generator = ExploitGenerator(debug=args.debug)
        
        if args.type == 'file':
            if args.subtype == 'pdf' or args.output.endswith('.pdf'):
                result = generator.generate_file_exploit('pdf', args.output, args.payload)
            elif args.subtype == 'zip' or args.output.endswith('.zip'):
                result = generator.generate_file_exploit('zip', args.output, args.payload)
            else:
                result = generator.generate_file_exploit('pdf', args.output, args.payload)
        
        elif args.type == 'image':
            if args.subtype == 'jpeg' or args.output.endswith(('.jpg', '.jpeg')):
                result = generator.generate_image_exploit('jpeg', args.output)
            elif args.subtype == 'png' or args.output.endswith('.png'):
                result = generator.generate_image_exploit('png', args.output)
            else:
                result = generator.generate_image_exploit('jpeg', args.output)
        
        elif args.type == 'voice':
            result = generator.generate_voice_exploit(args.output, args.subtype)
        
        console.print(f"[bold green]✓[/bold green] Exploit generated: {result['output_path']}")
        console.print(f"[dim]Type: {result['exploit_type']}[/dim]")
        console.print(f"[dim]Size: {result['file_size']} bytes[/dim]")
        
        return 0
        
    except Exception as e:
        console.print(f"[bold red]✗[/bold red] Generation failed: {str(e)}")
        return 1

def handle_test_command(args) -> int:
    """Handle the test command"""
    console.print(f"[bold yellow]Testing file: {args.file}[/bold yellow]")
    
    try:
        tester = ExploitTester(debug=args.debug)
        
        if args.analysis_only:
            result = tester.analyze_file(args.file)
            display_analysis_results(result)
        else:
            result = tester.test_exploit(args.file, safe_mode=args.safe_mode)
            display_test_results(result)
        
        return 0
        
    except Exception as e:
        console.print(f"[bold red]✗[/bold red] Testing failed: {str(e)}")
        return 1

def handle_analyze_command(args) -> int:
    """Handle the analyze command"""
    console.print(f"[bold cyan]Analyzing file: {args.file}[/bold cyan]")
    
    try:
        analyzer = ExploitAnalyzer(debug=args.debug)
        result = analyzer.analyze_file(args.file, detailed=args.detailed)
        
        if args.output_format == 'json':
            import json
            console.print(json.dumps(result, indent=2))
        elif args.output_format == 'html':
            html_report = analyzer.generate_html_report(result)
            console.print(f"HTML report generated: {html_report}")
        else:
            display_analysis_results(result)
        
        return 0
        
    except Exception as e:
        console.print(f"[bold red]✗[/bold red] Analysis failed: {str(e)}")
        return 1

def handle_demo_command(args) -> int:
    """Handle the demo command"""
    console.print(f"[bold magenta]Running demonstration: {args.scenario}[/bold magenta]")
    
    try:
        if args.scenario == 'file-exploit':
            run_file_exploit_demo(args.interactive)
        elif args.scenario == 'image-exploit':
            run_image_exploit_demo(args.interactive)
        elif args.scenario == 'voice-call':
            run_voice_call_demo(args.interactive)
        elif args.scenario == 'full-attack':
            run_full_attack_demo(args.interactive)
        
        return 0
        
    except Exception as e:
        console.print(f"[bold red]✗[/bold red] Demo failed: {str(e)}")
        return 1

def handle_payload_command(args) -> int:
    """Handle the payload command"""
    console.print(f"[bold blue]Payload {args.action}...[/bold blue]")
    
    try:
        manager = PayloadManager(debug=args.debug)
        
        if args.action == 'create':
            if not args.type or not args.code:
                console.print("[bold red]✗[/bold red] Type and code required for create")
                return 1
            
            with open(args.code, 'r') as f:
                payload_code = f.read()
            
            payload = manager.create_payload(args.type, payload_code)
            console.print(f"[bold green]✓[/bold green] Payload created: {payload['id']}")
            
        elif args.action == 'execute':
            if not args.id:
                console.print("[bold red]✗[/bold red] Payload ID required for execute")
                return 1
            
            result = manager.execute_payload(args.id)
            console.print(f"[bold green]✓[/bold green] Payload executed: {result['status']}")
            
        elif args.action == 'list':
            payloads = manager.list_active_payloads()
            display_payload_list(payloads)
            
        elif args.action == 'remove':
            if not args.id:
                console.print("[bold red]✗[/bold red] Payload ID required for remove")
                return 1
            
            success = manager.cleanup_payload(args.id)
            if success:
                console.print(f"[bold green]✓[/bold green] Payload removed: {args.id}")
            else:
                console.print(f"[bold red]✗[/bold red] Failed to remove payload: {args.id}")
        
        return 0
        
    except Exception as e:
        console.print(f"[bold red]✗[/bold red] Payload operation failed: {str(e)}")
        return 1

def display_analysis_results(result: Dict):
    """Display analysis results in a formatted table"""
    table = Table(title="Analysis Results")
    table.add_column("Property", style="cyan")
    table.add_column("Value", style="white")
    
    table.add_row("File Path", result.get('file_path', 'Unknown'))
    table.add_row("File Type", result.get('file_type', 'Unknown'))
    table.add_row("File Size", f"{result.get('file_size', 0)} bytes")
    table.add_row("Risk Level", result.get('risk_level', 'UNKNOWN'))
    
    if result.get('exploit_indicators'):
        indicators = '\n'.join(result['exploit_indicators'])
        table.add_row("Exploit Indicators", indicators)
    
    if result.get('suspicious_patterns'):
        patterns = '\n'.join(result['suspicious_patterns'])
        table.add_row("Suspicious Patterns", patterns)
    
    console.print(table)

def display_test_results(result: Dict):
    """Display test results"""
    if result.get('safe_mode', True):
        console.print("[bold yellow]Test run in safe mode - no actual execution[/bold yellow]")
    
    console.print(f"Test Status: {result.get('status', 'UNKNOWN')}")
    console.print(f"Execution Time: {result.get('execution_time', 0):.2f}s")
    
    if result.get('output'):
        console.print("\nOutput:")
        console.print(result['output'])

def display_payload_list(payloads: List[Dict]):
    """Display list of payloads"""
    if not payloads:
        console.print("No active payloads")
        return
    
    table = Table(title="Active Payloads")
    table.add_column("ID", style="cyan")
    table.add_column("Type", style="green")
    table.add_column("Status", style="yellow")
    table.add_column("Created", style="dim")
    
    for payload in payloads:
        import datetime
        created = datetime.datetime.fromtimestamp(payload['created_at']).strftime('%Y-%m-%d %H:%M:%S')
        table.add_row(
            payload['id'][:8] + '...',
            payload['type'],
            payload['status'],
            created
        )
    
    console.print(table)

def run_file_exploit_demo(interactive: bool):
    """Run file exploit demonstration"""
    console.print("[bold]File Exploit Demonstration[/bold]")
    
    file_exploit = FileExploit(debug=True)
    
    # Generate sample files
    console.print("1. Generating malicious PDF...")
    pdf_path = file_exploit.generate_malicious_pdf("demo_malicious.pdf")
    
    console.print("2. Generating malicious ZIP...")
    zip_path = file_exploit.generate_malicious_zip("demo_malicious.zip")
    
    # Analyze files
    console.print("3. Analyzing generated files...")
    pdf_analysis = file_exploit.analyze_file(pdf_path)
    zip_analysis = file_exploit.analyze_file(zip_path)
    
    console.print(f"PDF Risk Level: {pdf_analysis['risk_level']}")
    console.print(f"ZIP Risk Level: {zip_analysis['risk_level']}")
    
    if interactive:
        input("Press Enter to continue...")

def run_image_exploit_demo(interactive: bool):
    """Run image exploit demonstration"""
    console.print("[bold]Image Exploit Demonstration[/bold]")
    
    image_exploit = ImageExploit(debug=True)
    
    # Generate sample images
    console.print("1. Generating malicious JPEG...")
    jpeg_path = image_exploit.create_malicious_jpeg("demo_malicious.jpg")
    
    console.print("2. Generating malicious PNG...")
    png_path = image_exploit.create_malicious_png("demo_malicious.png")
    
    # Create steganographic image
    console.print("3. Creating steganographic image...")
    payload = b"Hidden malicious payload for demonstration"
    stego_path = image_exploit.create_steganographic_image("", payload, "demo_stego.png")
    
    # Analyze images
    console.print("4. Analyzing generated images...")
    jpeg_analysis = image_exploit.analyze_image_exploits(jpeg_path)
    png_analysis = image_exploit.analyze_image_exploits(png_path)
    
    console.print(f"JPEG Risk Level: {jpeg_analysis['risk_level']}")
    console.print(f"PNG Risk Level: {png_analysis['risk_level']}")
    
    if interactive:
        input("Press Enter to continue...")

def run_voice_call_demo(interactive: bool):
    """Run voice call exploit demonstration"""
    console.print("[bold]Voice Call Exploit Demonstration[/bold]")
    
    voice_exploit = VoiceExploit(debug=True)
    
    # Generate malicious audio
    console.print("1. Generating malicious audio file...")
    audio_path = voice_exploit.create_malicious_audio_file("demo_malicious.wav")
    
    # Simulate VoIP exploit
    console.print("2. Simulating VoIP call exploit...")
    voip_result = voice_exploit.simulate_voip_call_exploit()
    
    # Simulate real-time exploit
    console.print("3. Simulating real-time audio exploit...")
    realtime_result = voice_exploit.simulate_real_time_audio_exploit(3)
    
    console.print(f"VoIP Exploit Status: {voip_result['status']}")
    console.print(f"Real-time Exploits: {realtime_result['race_conditions_triggered']} race conditions")
    
    if interactive:
        input("Press Enter to continue...")

def run_full_attack_demo(interactive: bool):
    """Run full attack chain demonstration"""
    console.print("[bold]Full Attack Chain Demonstration[/bold]")

    # Step 1: Generate exploit
    console.print("1. Generating exploit...")
    file_exploit = FileExploit(debug=True)
    exploit_path = file_exploit.generate_malicious_pdf("full_demo_exploit.pdf")

    # Step 2: Payload delivery
    console.print("2. Simulating payload delivery...")
    payload_manager = PayloadManager(debug=True)
    payload_code = """
    import os
    print("Simulated payload execution")
    """
    payload = payload_manager.create_payload('reconnaissance', payload_code)
    delivery_result = payload_manager.deliver_payload(payload['id'])

    # Step 3: Stealth activation
    console.print("3. Activating stealth measures...")
    stealth_manager = StealthManager(debug=True)
    stealth_result = stealth_manager.enable_stealth_mode()

    # Step 4: Persistence establishment
    console.print("4. Establishing persistence...")
    persistence_manager = PersistenceManager(debug=True)
    persistence_result = persistence_manager.establish_persistence()

    # Step 5: Payload execution
    console.print("5. Executing payload...")
    execution_result = payload_manager.execute_payload(payload['id'])

    console.print(f"Attack Chain Status: {execution_result['status']}")
    console.print(f"Stealth Techniques: {len(stealth_result['activated_techniques'])}")
    console.print(f"Persistence Methods: {len(persistence_result['successful_techniques'])}")

    if interactive:
        input("Press Enter to continue...")

    # Cleanup
    console.print("6. Cleaning up demonstration...")
    stealth_manager.disable_stealth_mode()
    persistence_manager.remove_persistence()
    payload_manager.cleanup_payload(payload['id'])

    console.print("[bold green]✓[/bold green] Full attack demonstration completed")

def main():
    """Main entry point"""
    parser = create_parser()
    args = parser.parse_args()

    # Setup logging
    if args.debug:
        logging.basicConfig(level=logging.DEBUG)
    else:
        logging.basicConfig(level=logging.INFO)

    # Print banner
    print_banner()

    # Handle commands
    if not args.command:
        parser.print_help()
        return 1

    try:
        if args.command == 'generate':
            return handle_generate_command(args)
        elif args.command == 'test':
            return handle_test_command(args)
        elif args.command == 'analyze':
            return handle_analyze_command(args)
        elif args.command == 'demo':
            return handle_demo_command(args)
        elif args.command == 'payload':
            return handle_payload_command(args)
        else:
            console.print(f"[bold red]Unknown command: {args.command}[/bold red]")
            return 1

    except KeyboardInterrupt:
        console.print("\n[bold yellow]Operation cancelled by user[/bold yellow]")
        return 1
    except Exception as e:
        console.print(f"[bold red]Unexpected error: {str(e)}[/bold red]")
        if args.debug:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == '__main__':
    sys.exit(main())
