#!/usr/bin/env python3
"""
Basic Usage Examples for WhatsApp Zero-Day Exploit Simulation
Educational examples showing how to use the simulation framework

⚠️ EDUCATIONAL PURPOSE ONLY ⚠️
"""

import os
import sys

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from exploits import FileExploit, ImageExploit, VoiceExploit
from payloads import PayloadManager, StealthManager, PersistenceManager
from tools.generator import ExploitGenerator
from tools.tester import ExploitTester
from tools.analyzer import ExploitAnalyzer

def print_disclaimer():
    """Print educational disclaimer"""
    print("=" * 80)
    print("⚠️  EDUCATIONAL PURPOSE ONLY ⚠️")
    print("This code is for educational and research purposes only.")
    print("Using these techniques for malicious purposes is illegal and unethical.")
    print("Always follow responsible disclosure practices.")
    print("=" * 80)
    print()

def example_file_exploit():
    """Example: Generate and analyze a file-based exploit"""
    print("📄 File Exploit Example")
    print("-" * 40)
    
    # Create file exploit instance
    file_exploit = FileExploit(debug=True)
    
    # Generate a malicious PDF
    print("1. Generating malicious PDF...")
    pdf_path = file_exploit.generate_malicious_pdf("example_malicious.pdf")
    print(f"   Generated: {pdf_path}")
    
    # Analyze the generated file
    print("2. Analyzing the generated PDF...")
    analysis = file_exploit.analyze_file(pdf_path)
    print(f"   Risk Level: {analysis['risk_level']}")
    print(f"   Suspicious Patterns: {len(analysis['suspicious_patterns'])}")
    
    print("✓ File exploit example completed\n")

def example_image_exploit():
    """Example: Generate and analyze an image-based exploit"""
    print("🖼️  Image Exploit Example")
    print("-" * 40)
    
    # Create image exploit instance
    image_exploit = ImageExploit(debug=True)
    
    # Generate a malicious JPEG
    print("1. Generating malicious JPEG...")
    jpeg_path = image_exploit.create_malicious_jpeg("example_malicious.jpg")
    print(f"   Generated: {jpeg_path}")
    
    # Create steganographic image
    print("2. Creating steganographic image...")
    payload = b"Hidden payload for educational demonstration"
    stego_path = image_exploit.create_steganographic_image("", payload, "example_stego.png")
    print(f"   Generated: {stego_path}")
    
    # Extract hidden payload
    print("3. Extracting hidden payload...")
    extracted = image_exploit.extract_hidden_payload(stego_path)
    print(f"   Extracted: {extracted[:50]}...")
    
    print("✓ Image exploit example completed\n")

def example_voice_exploit():
    """Example: Generate and analyze a voice call exploit"""
    print("🎵 Voice Call Exploit Example")
    print("-" * 40)
    
    # Create voice exploit instance
    voice_exploit = VoiceExploit(debug=True)
    
    # Generate malicious audio file
    print("1. Generating malicious audio file...")
    audio_path = voice_exploit.create_malicious_audio_file("example_malicious.wav")
    print(f"   Generated: {audio_path}")
    
    # Simulate VoIP call exploit
    print("2. Simulating VoIP call exploit...")
    voip_result = voice_exploit.simulate_voip_call_exploit()
    print(f"   Status: {voip_result['status']}")
    print(f"   Steps: {len(voip_result['steps'])}")
    
    print("✓ Voice exploit example completed\n")

def example_payload_management():
    """Example: Payload creation and management"""
    print("🎯 Payload Management Example")
    print("-" * 40)
    
    # Create payload manager
    payload_manager = PayloadManager(debug=True)
    
    # Create a reconnaissance payload
    print("1. Creating reconnaissance payload...")
    payload_code = """
import os
import platform

def gather_system_info():
    print("Gathering system information (SIMULATION)")
    return {
        'os': platform.system(),
        'user': os.getenv('USERNAME', 'unknown'),
        'cwd': os.getcwd()
    }

gather_system_info()
"""
    
    payload = payload_manager.create_payload('reconnaissance', payload_code)
    print(f"   Payload ID: {payload['id']}")
    
    # Deliver and execute payload
    print("2. Delivering payload...")
    delivery_result = payload_manager.deliver_payload(payload['id'])
    print(f"   Delivery Status: {delivery_result['status']}")
    
    print("3. Executing payload...")
    execution_result = payload_manager.execute_payload(payload['id'])
    print(f"   Execution Status: {execution_result['status']}")
    
    print("✓ Payload management example completed\n")

def example_stealth_techniques():
    """Example: Stealth and evasion techniques"""
    print("🥷 Stealth Techniques Example")
    print("-" * 40)
    
    # Create stealth manager
    stealth_manager = StealthManager(debug=True)
    
    # Enable stealth mode
    print("1. Enabling stealth mode...")
    stealth_result = stealth_manager.enable_stealth_mode()
    print(f"   Activated Techniques: {len(stealth_result['activated_techniques'])}")
    
    # Monitor for detection
    print("2. Monitoring for detection attempts...")
    detection_result = stealth_manager.monitor_detection_attempts()
    print(f"   Detection Attempts: {detection_result['total_attempts']}")
    
    # Resource throttling
    print("3. Checking resource usage...")
    throttling_result = stealth_manager.resource_throttling()
    print(f"   Throttling Applied: {throttling_result['throttling_applied']}")
    
    # Disable stealth mode
    print("4. Disabling stealth mode...")
    cleanup_result = stealth_manager.disable_stealth_mode()
    print(f"   Cleaned Techniques: {len(cleanup_result['cleaned_techniques'])}")
    
    print("✓ Stealth techniques example completed\n")

def example_persistence_mechanisms():
    """Example: Persistence establishment"""
    print("🔒 Persistence Mechanisms Example")
    print("-" * 40)
    
    # Create persistence manager
    persistence_manager = PersistenceManager(debug=True)
    
    # Establish persistence
    print("1. Establishing persistence...")
    persistence_result = persistence_manager.establish_persistence()
    print(f"   Successful Techniques: {len(persistence_result['successful_techniques'])}")
    
    # Verify persistence
    print("2. Verifying persistence...")
    verification_result = persistence_manager.verify_persistence()
    print(f"   Overall Status: {verification_result['overall_status']}")
    
    # Remove persistence
    print("3. Removing persistence...")
    removal_result = persistence_manager.remove_persistence()
    print(f"   Removed Techniques: {len(removal_result['removed_techniques'])}")
    
    print("✓ Persistence mechanisms example completed\n")

def example_full_attack_chain():
    """Example: Complete attack chain simulation"""
    print("⚔️  Full Attack Chain Example")
    print("-" * 40)
    
    # Step 1: Generate exploit
    print("1. Generating exploit...")
    generator = ExploitGenerator(debug=True)
    exploit_result = generator.generate_file_exploit('pdf', 'attack_chain_exploit.pdf')
    print(f"   Exploit Generated: {exploit_result['success']}")
    
    # Step 2: Create payload
    print("2. Creating payload...")
    payload_manager = PayloadManager(debug=True)
    payload_code = "print('Attack payload executed (SIMULATION)')"
    payload = payload_manager.create_payload('reconnaissance', payload_code)
    print(f"   Payload Created: {payload['id'][:8]}...")
    
    # Step 3: Enable stealth
    print("3. Enabling stealth...")
    stealth_manager = StealthManager(debug=True)
    stealth_result = stealth_manager.enable_stealth_mode()
    print(f"   Stealth Techniques: {len(stealth_result['activated_techniques'])}")
    
    # Step 4: Establish persistence
    print("4. Establishing persistence...")
    persistence_manager = PersistenceManager(debug=True)
    persistence_result = persistence_manager.establish_persistence()
    print(f"   Persistence Methods: {len(persistence_result['successful_techniques'])}")
    
    # Step 5: Execute payload
    print("5. Executing payload...")
    payload_manager.deliver_payload(payload['id'])
    execution_result = payload_manager.execute_payload(payload['id'])
    print(f"   Execution Status: {execution_result['status']}")
    
    # Step 6: Cleanup
    print("6. Cleaning up...")
    stealth_manager.disable_stealth_mode()
    persistence_manager.remove_persistence()
    payload_manager.cleanup_payload(payload['id'])
    print("   Cleanup completed")
    
    print("✓ Full attack chain example completed\n")

def example_analysis_tools():
    """Example: Using analysis tools"""
    print("🔍 Analysis Tools Example")
    print("-" * 40)
    
    # Generate a sample file to analyze
    file_exploit = FileExploit(debug=True)
    sample_file = file_exploit.generate_malicious_pdf("analysis_sample.pdf")
    
    # Use the analyzer
    print("1. Analyzing file with detailed analysis...")
    analyzer = ExploitAnalyzer(debug=True)
    analysis_result = analyzer.analyze_file(sample_file, detailed=True)
    
    print(f"   File Type: {analysis_result['file_info']['file_type']}")
    print(f"   Risk Level: {analysis_result['security_analysis']['risk_level']}")
    print(f"   Recommendations: {len(analysis_result['recommendations'])}")
    
    # Generate HTML report
    print("2. Generating HTML report...")
    html_report = analyzer.generate_html_report(analysis_result)
    print(f"   HTML Report: {html_report}")
    
    # Use the tester
    print("3. Testing file safely...")
    tester = ExploitTester(debug=True)
    test_result = tester.test_exploit(sample_file, safe_mode=True)
    print(f"   Test Status: {test_result['status']}")
    print(f"   Safe Mode: {test_result['safe_mode']}")
    
    print("✓ Analysis tools example completed\n")

def main():
    """Run all examples"""
    print_disclaimer()
    
    print("🚀 WhatsApp Zero-Day Exploit Simulation Examples")
    print("=" * 60)
    print()
    
    try:
        # Run all examples
        example_file_exploit()
        example_image_exploit()
        example_voice_exploit()
        example_payload_management()
        example_stealth_techniques()
        example_persistence_mechanisms()
        example_full_attack_chain()
        example_analysis_tools()
        
        print("🎉 All examples completed successfully!")
        print("\nGenerated files:")
        for filename in os.listdir('.'):
            if filename.startswith(('example_', 'attack_chain_', 'analysis_')):
                print(f"  - {filename}")
        
    except Exception as e:
        print(f"❌ Error running examples: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("\n" + "=" * 60)
        print("Remember: This is for educational purposes only!")
        print("Use this knowledge responsibly for security research and defense.")

if __name__ == "__main__":
    main()
