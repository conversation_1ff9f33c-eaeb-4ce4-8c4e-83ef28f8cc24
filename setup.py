#!/usr/bin/env python3
"""
WhatsApp Zero-Day Exploit Simulation Setup
Educational and Research Purposes Only
"""

from setuptools import setup, find_packages
import os

# Read README for long description
with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# Read requirements
with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="whatsapp-zero-day-sim",
    version="1.0.0",
    author="Security Research Team",
    author_email="<EMAIL>",
    description="Educational simulation of WhatsApp zero-day exploits",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/security-research/whatsapp-zero-day-sim",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Education",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Security",
        "Topic :: Education",
        "Topic :: Scientific/Engineering :: Information Analysis",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "whatsapp-exploit-sim=tools.main:main",
            "exploit-generator=tools.generator.main:main",
            "exploit-tester=tools.tester.main:main",
            "exploit-analyzer=tools.analyzer.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.md", "*.txt", "*.json", "*.yaml"],
        "examples": ["*"],
        "docs": ["*"],
    },
    zip_safe=False,
    keywords="security, research, education, whatsapp, exploit, simulation",
    project_urls={
        "Bug Reports": "https://github.com/security-research/whatsapp-zero-day-sim/issues",
        "Source": "https://github.com/security-research/whatsapp-zero-day-sim",
        "Documentation": "https://github.com/security-research/whatsapp-zero-day-sim/docs",
    },
)
