"""
Payload Delivery and Execution Simulation Package
Educational demonstration of payload systems

⚠️ EDUCATIONAL PURPOSE ONLY ⚠️
"""

try:
    from .payload_manager import PayloadManager
    from .stealth.stealth_manager import StealthManager
    from .persistence.persistence_manager import PersistenceManager
except ImportError:
    # Fallback imports for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(__file__))
    from payload_manager import PayloadManager
    from stealth.stealth_manager import StealthManager
    from persistence.persistence_manager import PersistenceManager

# Note: ExfiltrationManager not implemented yet
ExfiltrationManager = None

__all__ = [
    'PayloadManager',
    'StealthManager',
    'PersistenceManager'
]

# Educational disclaimer
DISCLAIMER = """
⚠️  EDUCATIONAL PURPOSE ONLY ⚠️

These payload simulations are for educational and research purposes only.
Using these techniques for malicious purposes is illegal and unethical.
Always follow responsible disclosure practices.
"""

def print_disclaimer():
    """Print the educational disclaimer"""
    print(DISCLAIMER)
