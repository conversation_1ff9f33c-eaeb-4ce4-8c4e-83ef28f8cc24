#!/usr/bin/env python3
"""
Basic Test Script for WhatsApp Zero-Day Exploit Simulation
Quick test to verify all components work correctly

⚠️ EDUCATIONAL PURPOSE ONLY ⚠️
"""

def test_imports():
    """Test that all modules can be imported"""
    print("Testing imports...")
    
    try:
        from exploits import FileExploit, ImageExploit, VoiceExploit
        print("✅ Exploit modules imported successfully")
    except Exception as e:
        print(f"❌ Exploit import failed: {e}")
        return False
    
    try:
        from payloads import PayloadManager, StealthManager, PersistenceManager
        print("✅ Payload modules imported successfully")
    except Exception as e:
        print(f"❌ Payload import failed: {e}")
        return False
    
    return True

def test_file_exploit():
    """Test file exploit generation"""
    print("\nTesting file exploit...")
    
    try:
        from exploits import FileExploit
        
        file_exploit = FileExploit(debug=False)
        pdf_path = file_exploit.generate_malicious_pdf("test_malicious.pdf")
        
        print(f"✅ Generated malicious PDF: {pdf_path}")
        
        # Test analysis
        analysis = file_exploit.analyze_file(pdf_path)
        print(f"✅ Analysis completed - Risk Level: {analysis['risk_level']}")
        
        return True
    except Exception as e:
        print(f"❌ File exploit test failed: {e}")
        return False

def test_payload_system():
    """Test payload management"""
    print("\nTesting payload system...")
    
    try:
        from payloads import PayloadManager
        
        payload_manager = PayloadManager(debug=False)
        
        # Create simple payload
        payload_code = "print('Test payload executed (SIMULATION)')"
        payload = payload_manager.create_payload('reconnaissance', payload_code)
        
        print(f"✅ Payload created: {payload['id'][:8]}...")
        
        # Deliver and execute
        payload_manager.deliver_payload(payload['id'])
        result = payload_manager.execute_payload(payload['id'])
        
        print(f"✅ Payload executed: {result['status']}")
        
        return True
    except Exception as e:
        print(f"❌ Payload system test failed: {e}")
        return False

def test_stealth_system():
    """Test stealth management"""
    print("\nTesting stealth system...")
    
    try:
        from payloads import StealthManager
        
        stealth_manager = StealthManager(debug=False)
        
        # Enable stealth
        result = stealth_manager.enable_stealth_mode()
        print(f"✅ Stealth enabled: {len(result['activated_techniques'])} techniques")
        
        # Disable stealth
        cleanup = stealth_manager.disable_stealth_mode()
        print(f"✅ Stealth disabled: {len(cleanup['cleaned_techniques'])} techniques cleaned")
        
        return True
    except Exception as e:
        print(f"❌ Stealth system test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 WhatsApp Zero-Day Exploit Simulation - Basic Test")
    print("=" * 60)
    print("⚠️  EDUCATIONAL PURPOSE ONLY ⚠️")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_file_exploit,
        test_payload_system,
        test_stealth_system
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The simulation framework is working correctly.")
        print("\nYou can now:")
        print("  • Run 'python demo.py' for the full interactive demonstration")
        print("  • Run 'python examples/basic_usage.py' for usage examples")
        print("  • Use 'python -m tools.main --help' for CLI tools")
    else:
        print("⚠️  Some tests failed. Check the error messages above.")
    
    print("\n" + "=" * 60)
    print("Remember: This is for educational purposes only!")

if __name__ == "__main__":
    main()
