"""
WhatsApp Zero-Day Exploit Simulation Package
Educational and Research Purposes Only

This package contains simulated exploit modules for educational purposes.
DO NOT USE FOR MALICIOUS ACTIVITIES.
"""

__version__ = "1.0.0"
__author__ = "Security Research Team"
__email__ = "<EMAIL>"

# Import main exploit classes
from .file_based.file_exploit import FileExploit
from .image_based.image_exploit import ImageExploit
from .voice_call.voice_exploit import VoiceExploit

__all__ = [
    'FileExploit',
    'ImageExploit', 
    'VoiceExploit'
]

# Educational disclaimer
DISCLAIMER = """
⚠️  EDUCATIONAL PURPOSE ONLY ⚠️

This code is for educational and research purposes only.
Using this for malicious purposes is illegal and unethical.
Always follow responsible disclosure practices.
"""

def print_disclaimer():
    """Print the educational disclaimer"""
    print(DISCLAIMER)
