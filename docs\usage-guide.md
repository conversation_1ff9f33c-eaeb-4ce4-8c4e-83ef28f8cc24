# Usage Guide

This guide explains how to use the WhatsApp Zero-Day Exploit Simulation framework for educational purposes.

## ⚠️ Important Disclaimer

**This framework is for educational and research purposes only. Using these techniques for malicious purposes is illegal and unethical. Always follow responsible disclosure practices.**

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd zero-day-exploit
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Install the package:
```bash
python setup.py install
```

## Command Line Interface

The main CLI tool provides several commands for generating, testing, and analyzing exploits.

### Generate Exploits

Generate different types of exploit samples:

```bash
# Generate a malicious PDF
python -m tools.main generate --type file --subtype pdf --output malicious.pdf

# Generate a malicious image
python -m tools.main generate --type image --subtype jpeg --output malicious.jpg

# Generate a voice call exploit
python -m tools.main generate --type voice --output malicious.wav
```

### Test Exploits

Safely test exploit samples:

```bash
# Analyze a file without execution
python -m tools.main test --file malicious.pdf --analysis-only

# Test a file in safe mode
python -m tools.main test --file malicious.jpg --safe-mode
```

### Analyze Files

Perform detailed analysis of files:

```bash
# Basic analysis
python -m tools.main analyze --file suspicious.pdf

# Detailed analysis with HTML report
python -m tools.main analyze --file suspicious.jpg --detailed --output-format html
```

### Run Demonstrations

Run educational demonstrations:

```bash
# File exploit demonstration
python -m tools.main demo --scenario file-exploit

# Full attack chain demonstration
python -m tools.main demo --scenario full-attack --interactive
```

### Payload Management

Manage payloads:

```bash
# Create a payload
python -m tools.main payload --action create --type reconnaissance --code payload.py

# List active payloads
python -m tools.main payload --action list

# Execute a payload
python -m tools.main payload --action execute --id <payload-id>
```

## Python API Usage

### File-Based Exploits

```python
from exploits import FileExploit

# Create file exploit instance
file_exploit = FileExploit(debug=True)

# Generate malicious PDF
pdf_path = file_exploit.generate_malicious_pdf("malicious.pdf")

# Analyze the file
analysis = file_exploit.analyze_file(pdf_path)
print(f"Risk Level: {analysis['risk_level']}")
```

### Image-Based Exploits

```python
from exploits import ImageExploit

# Create image exploit instance
image_exploit = ImageExploit(debug=True)

# Generate malicious JPEG
jpeg_path = image_exploit.create_malicious_jpeg("malicious.jpg")

# Create steganographic image
payload = b"Hidden malicious payload"
stego_path = image_exploit.create_steganographic_image("", payload, "stego.png")

# Extract hidden payload
extracted = image_exploit.extract_hidden_payload(stego_path)
```

### Voice Call Exploits

```python
from exploits import VoiceExploit

# Create voice exploit instance
voice_exploit = VoiceExploit(debug=True)

# Generate malicious audio
audio_path = voice_exploit.create_malicious_audio_file("malicious.wav")

# Simulate VoIP exploit
voip_result = voice_exploit.simulate_voip_call_exploit()
print(f"Status: {voip_result['status']}")
```

### Payload Management

```python
from payloads import PayloadManager

# Create payload manager
payload_manager = PayloadManager(debug=True)

# Create payload
payload_code = """
import os
print("Simulated payload execution")
"""
payload = payload_manager.create_payload('reconnaissance', payload_code)

# Deliver and execute
payload_manager.deliver_payload(payload['id'])
result = payload_manager.execute_payload(payload['id'])
```

### Stealth Techniques

```python
from payloads import StealthManager

# Create stealth manager
stealth_manager = StealthManager(debug=True)

# Enable stealth mode
stealth_result = stealth_manager.enable_stealth_mode()
print(f"Activated: {len(stealth_result['activated_techniques'])} techniques")

# Monitor for detection
detection_result = stealth_manager.monitor_detection_attempts()

# Disable stealth mode
stealth_manager.disable_stealth_mode()
```

### Persistence Mechanisms

```python
from payloads import PersistenceManager

# Create persistence manager
persistence_manager = PersistenceManager(debug=True)

# Establish persistence
persistence_result = persistence_manager.establish_persistence()
print(f"Established: {len(persistence_result['successful_techniques'])} methods")

# Verify persistence
verification = persistence_manager.verify_persistence()
print(f"Status: {verification['overall_status']}")

# Remove persistence
persistence_manager.remove_persistence()
```

## Analysis Tools

### Exploit Generator

```python
from tools.generator import ExploitGenerator

generator = ExploitGenerator(debug=True)

# Generate file exploit
result = generator.generate_file_exploit('pdf', 'output.pdf')

# Generate image exploit
result = generator.generate_image_exploit('jpeg', 'output.jpg')

# Batch generation
configs = [
    {'type': 'file', 'file_type': 'pdf', 'output_path': 'batch1.pdf'},
    {'type': 'image', 'image_type': 'png', 'output_path': 'batch2.png'}
]
batch_result = generator.generate_batch_exploits(configs)
```

### Exploit Tester

```python
from tools.tester import ExploitTester

tester = ExploitTester(debug=True)

# Test single file
test_result = tester.test_exploit('malicious.pdf', safe_mode=True)

# Batch testing
files = ['file1.pdf', 'file2.jpg', 'file3.wav']
batch_result = tester.batch_test_exploits(files)
```

### Exploit Analyzer

```python
from tools.analyzer import ExploitAnalyzer

analyzer = ExploitAnalyzer(debug=True)

# Analyze file
analysis = analyzer.analyze_file('suspicious.pdf', detailed=True)

# Generate HTML report
html_report = analyzer.generate_html_report(analysis)
```

## Configuration

### Payload Manager Configuration

```python
payload_manager.config = {
    'encryption_enabled': True,
    'stealth_mode': True,
    'persistence_enabled': True,
    'exfiltration_enabled': False,  # Keep disabled for safety
    'max_payload_size': 10 * 1024 * 1024,
    'execution_delay': 5,
    'cleanup_after_execution': True
}
```

### Stealth Manager Configuration

```python
stealth_manager.config = {
    'process_hiding_enabled': True,
    'network_hiding_enabled': True,
    'file_hiding_enabled': True,
    'anti_analysis_enabled': True,
    'resource_throttling_enabled': True,
    'max_cpu_usage': 5.0,
    'max_memory_usage': 50 * 1024 * 1024
}
```

## Examples

See the `examples/` directory for complete usage examples:

- `basic_usage.py` - Basic usage examples for all components
- `advanced_scenarios.py` - Advanced attack scenarios
- `analysis_examples.py` - File analysis examples

## Safety Guidelines

1. **Always run in safe mode** - The framework enforces safe mode by default
2. **Use isolated environments** - Run in virtual machines or containers
3. **Educational purposes only** - Never use for malicious activities
4. **Responsible disclosure** - Report real vulnerabilities responsibly
5. **Keep updated** - Regularly update dependencies for security

## Troubleshooting

### Common Issues

1. **Import errors**: Ensure all dependencies are installed
2. **Permission errors**: Run with appropriate permissions
3. **File not found**: Check file paths and working directory
4. **Analysis failures**: Some files may not be analyzable

### Debug Mode

Enable debug mode for detailed logging:

```python
# In Python
exploit = FileExploit(debug=True)

# Command line
python -m tools.main --debug generate --type file --output test.pdf
```

## Contributing

This is an educational project. Contributions should focus on:
- Improving educational value
- Adding defensive measures
- Enhancing documentation
- Adding new attack vector simulations

## Support

For questions or issues:
1. Check the documentation
2. Review the examples
3. Enable debug mode for detailed logs
4. Consult the source code comments

Remember: Use this knowledge responsibly for security research and education!
