#!/usr/bin/env python3
"""
Exploit Analyzer - Analyze and report on exploit samples
Educational tool for detailed analysis of exploit samples

⚠️ EDUCATIONAL PURPOSE ONLY ⚠️
"""

import os
import time
import json
import hashlib
from typing import Dict, List, Optional, Any
import logging

# Import our exploit modules for analysis
from exploits.file_based.file_exploit import FileExploit
from exploits.image_based.image_exploit import ImageExploit
from exploits.voice_call.voice_exploit import VoiceExploit

class ExploitAnalyzer:
    """
    Analyze exploit samples and generate detailed reports
    """
    
    def __init__(self, debug: bool = False):
        """Initialize the exploit analyzer"""
        self.debug = debug
        self.logger = self._setup_logger()
        
        # Initialize analysis modules
        self.file_exploit = FileExploit(debug=debug)
        self.image_exploit = ImageExploit(debug=debug)
        self.voice_exploit = VoiceExploit(debug=debug)
        
        # Analysis configuration
        self.config = {
            'detailed_analysis': True,
            'generate_hashes': True,
            'entropy_analysis': True,
            'pattern_matching': True,
            'metadata_extraction': True,
            'behavioral_analysis': True
        }
        
        # Analysis history
        self.analysis_history = []
        
        self.logger.warning("⚠️ EDUCATIONAL PURPOSE ONLY - DO NOT USE MALICIOUSLY ⚠️")
    
    def _setup_logger(self) -> logging.Logger:
        """Setup logging for the exploit analyzer"""
        logger = logging.getLogger('ExploitAnalyzer')
        logger.setLevel(logging.DEBUG if self.debug else logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def analyze_file(self, file_path: str, detailed: bool = True) -> Dict:
        """
        Perform comprehensive analysis of an exploit file
        
        Args:
            file_path: Path to the file to analyze
            detailed: Whether to perform detailed analysis
            
        Returns:
            Dictionary with comprehensive analysis results
        """
        self.logger.info(f"Analyzing file: {file_path}")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        start_time = time.time()
        
        # Initialize analysis result
        analysis_result = {
            'file_path': file_path,
            'analysis_timestamp': start_time,
            'file_info': {},
            'security_analysis': {},
            'exploit_analysis': {},
            'behavioral_analysis': {},
            'recommendations': [],
            'analysis_time': 0
        }
        
        try:
            # Step 1: Basic file information
            analysis_result['file_info'] = self._analyze_file_info(file_path)
            
            # Step 2: Security analysis
            analysis_result['security_analysis'] = self._perform_security_analysis(file_path)
            
            # Step 3: Exploit-specific analysis
            analysis_result['exploit_analysis'] = self._perform_exploit_analysis(file_path, detailed)
            
            # Step 4: Behavioral analysis (if detailed)
            if detailed:
                analysis_result['behavioral_analysis'] = self._perform_behavioral_analysis(file_path)
            
            # Step 5: Generate recommendations
            analysis_result['recommendations'] = self._generate_recommendations(analysis_result)
            
        except Exception as e:
            self.logger.error(f"Analysis failed: {str(e)}")
            analysis_result['error'] = str(e)
        
        finally:
            analysis_result['analysis_time'] = time.time() - start_time
            
            # Record analysis in history
            self.analysis_history.append({
                'file_path': file_path,
                'timestamp': start_time,
                'analysis_time': analysis_result['analysis_time'],
                'risk_level': analysis_result.get('security_analysis', {}).get('risk_level', 'UNKNOWN')
            })
        
        self.logger.info(f"Analysis completed in {analysis_result['analysis_time']:.2f}s")
        return analysis_result
    
    def _analyze_file_info(self, file_path: str) -> Dict:
        """Analyze basic file information"""
        file_info = {
            'filename': os.path.basename(file_path),
            'file_size': os.path.getsize(file_path),
            'file_extension': os.path.splitext(file_path)[1].lower(),
            'creation_time': os.path.getctime(file_path),
            'modification_time': os.path.getmtime(file_path),
            'file_type': 'Unknown',
            'hashes': {}
        }
        
        # Detect file type
        file_info['file_type'] = self._detect_file_type(file_path)
        
        # Generate file hashes
        if self.config['generate_hashes']:
            file_info['hashes'] = self._generate_file_hashes(file_path)
        
        return file_info
    
    def _detect_file_type(self, file_path: str) -> str:
        """Detect file type based on magic bytes"""
        try:
            with open(file_path, 'rb') as f:
                header = f.read(16)
            
            if header.startswith(b'%PDF'):
                return 'PDF'
            elif header.startswith(b'PK\x03\x04'):
                return 'ZIP/Office'
            elif header.startswith(b'\x89PNG'):
                return 'PNG'
            elif header.startswith(b'\xff\xd8\xff'):
                return 'JPEG'
            elif header.startswith(b'GIF87a') or header.startswith(b'GIF89a'):
                return 'GIF'
            elif header.startswith(b'RIFF') and b'WAVE' in header:
                return 'WAV'
            elif header.startswith(b'ID3') or header[0:2] == b'\xff\xfb':
                return 'MP3'
            else:
                return 'Unknown'
        except Exception:
            return 'Unknown'
    
    def _generate_file_hashes(self, file_path: str) -> Dict:
        """Generate various hashes for the file"""
        hashes = {}
        
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
            
            hashes['md5'] = hashlib.md5(content).hexdigest()
            hashes['sha1'] = hashlib.sha1(content).hexdigest()
            hashes['sha256'] = hashlib.sha256(content).hexdigest()
            
        except Exception as e:
            self.logger.error(f"Failed to generate hashes: {str(e)}")
        
        return hashes
    
    def _perform_security_analysis(self, file_path: str) -> Dict:
        """Perform security-focused analysis"""
        security_analysis = {
            'risk_level': 'LOW',
            'threat_indicators': [],
            'suspicious_patterns': [],
            'entropy_analysis': {},
            'signature_matches': []
        }
        
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
            
            # Entropy analysis
            if self.config['entropy_analysis']:
                security_analysis['entropy_analysis'] = self._analyze_entropy(content)
            
            # Pattern matching
            if self.config['pattern_matching']:
                patterns = self._find_suspicious_patterns(content)
                security_analysis['suspicious_patterns'] = patterns
            
            # Determine risk level
            security_analysis['risk_level'] = self._calculate_risk_level(security_analysis)
            
        except Exception as e:
            self.logger.error(f"Security analysis failed: {str(e)}")
            security_analysis['error'] = str(e)
            security_analysis['risk_level'] = 'HIGH'
        
        return security_analysis
    
    def _analyze_entropy(self, content: bytes) -> Dict:
        """Analyze entropy of file content"""
        if not content:
            return {'entropy': 0, 'assessment': 'No data'}
        
        # Calculate Shannon entropy
        frequency = {}
        for byte in content:
            frequency[byte] = frequency.get(byte, 0) + 1
        
        entropy = 0
        content_len = len(content)
        for count in frequency.values():
            probability = count / content_len
            if probability > 0:
                import math
                entropy -= probability * math.log2(probability)
        
        # Assess entropy level
        if entropy < 3.0:
            assessment = 'Very low (likely text or simple data)'
        elif entropy < 5.0:
            assessment = 'Low (structured data)'
        elif entropy < 7.0:
            assessment = 'Medium (mixed content)'
        elif entropy < 7.8:
            assessment = 'High (compressed or encrypted)'
        else:
            assessment = 'Very high (likely encrypted or random)'
        
        return {
            'entropy': entropy,
            'assessment': assessment,
            'suspicious': entropy > 7.5
        }
    
    def _find_suspicious_patterns(self, content: bytes) -> List[str]:
        """Find suspicious patterns in file content"""
        patterns = []
        
        # Buffer overflow patterns
        if b'A' * 100 in content:
            patterns.append('Potential buffer overflow pattern (repeated A)')
        if b'B' * 100 in content:
            patterns.append('Potential buffer overflow pattern (repeated B)')
        
        # Script injection patterns
        if b'<script>' in content:
            patterns.append('JavaScript injection pattern')
        if b'eval(' in content:
            patterns.append('Dynamic code evaluation pattern')
        
        # Shellcode patterns
        if b'\x90\x90\x90\x90' in content:  # NOP sled
            patterns.append('Potential NOP sled (shellcode indicator)')
        
        # Path traversal patterns
        if b'../../../' in content:
            patterns.append('Path traversal pattern')
        
        # Executable patterns
        if b'MZ' in content[:2]:  # PE header
            patterns.append('Embedded PE executable')
        if b'\x7fELF' in content:  # ELF header
            patterns.append('Embedded ELF executable')
        
        return patterns
    
    def _calculate_risk_level(self, security_analysis: Dict) -> str:
        """Calculate overall risk level"""
        risk_score = 0
        
        # Entropy risk
        entropy_info = security_analysis.get('entropy_analysis', {})
        if entropy_info.get('suspicious', False):
            risk_score += 3
        
        # Pattern risk
        patterns = security_analysis.get('suspicious_patterns', [])
        risk_score += len(patterns)
        
        # Determine risk level
        if risk_score >= 5:
            return 'HIGH'
        elif risk_score >= 2:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def _perform_exploit_analysis(self, file_path: str, detailed: bool) -> Dict:
        """Perform exploit-specific analysis"""
        file_type = self._detect_file_type(file_path)
        
        if file_type == 'PDF':
            return self.file_exploit.analyze_file(file_path)
        elif file_type in ['PNG', 'JPEG', 'GIF']:
            return self.image_exploit.analyze_image_exploits(file_path)
        elif file_type in ['WAV', 'MP3']:
            return self.voice_exploit.analyze_audio_exploits(file_path)
        else:
            return self._generic_exploit_analysis(file_path)
    
    def _generic_exploit_analysis(self, file_path: str) -> Dict:
        """Perform generic exploit analysis"""
        return {
            'file_type': 'Unknown',
            'exploit_indicators': ['Unknown file type - limited analysis'],
            'risk_level': 'MEDIUM'
        }
    
    def _perform_behavioral_analysis(self, file_path: str) -> Dict:
        """Perform behavioral analysis"""
        behavioral_analysis = {
            'potential_behaviors': [],
            'system_interactions': [],
            'network_indicators': [],
            'persistence_indicators': []
        }
        
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
            
            # Look for behavioral indicators
            if b'CreateProcess' in content or b'ShellExecute' in content:
                behavioral_analysis['potential_behaviors'].append('Process creation')
            
            if b'RegSetValue' in content or b'RegCreateKey' in content:
                behavioral_analysis['system_interactions'].append('Registry modification')
            
            if b'http://' in content or b'https://' in content:
                behavioral_analysis['network_indicators'].append('HTTP/HTTPS URLs found')
            
            if b'startup' in content.lower() or b'autorun' in content.lower():
                behavioral_analysis['persistence_indicators'].append('Startup/autorun references')
            
        except Exception as e:
            self.logger.error(f"Behavioral analysis failed: {str(e)}")
            behavioral_analysis['error'] = str(e)
        
        return behavioral_analysis
    
    def _generate_recommendations(self, analysis_result: Dict) -> List[str]:
        """Generate security recommendations based on analysis"""
        recommendations = []
        
        risk_level = analysis_result.get('security_analysis', {}).get('risk_level', 'UNKNOWN')
        
        if risk_level == 'HIGH':
            recommendations.extend([
                'Do not open this file on production systems',
                'Analyze in isolated sandbox environment only',
                'Consider this file potentially malicious',
                'Report to security team if found in email/downloads'
            ])
        elif risk_level == 'MEDIUM':
            recommendations.extend([
                'Exercise caution when opening this file',
                'Scan with updated antivirus before opening',
                'Open in restricted environment if possible'
            ])
        else:
            recommendations.extend([
                'File appears relatively safe',
                'Standard security precautions recommended'
            ])
        
        # Add specific recommendations based on file type
        file_type = analysis_result.get('file_info', {}).get('file_type', 'Unknown')
        
        if file_type == 'PDF':
            recommendations.append('Disable JavaScript in PDF reader')
        elif file_type in ['PNG', 'JPEG']:
            recommendations.append('Use updated image viewers with security patches')
        elif file_type in ['WAV', 'MP3']:
            recommendations.append('Use updated audio players with codec security patches')
        
        return recommendations
    
    def generate_html_report(self, analysis_result: Dict) -> str:
        """Generate HTML report from analysis results"""
        html_template = """
<!DOCTYPE html>
<html>
<head>
    <title>Exploit Analysis Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 10px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .risk-high { color: red; font-weight: bold; }
        .risk-medium { color: orange; font-weight: bold; }
        .risk-low { color: green; font-weight: bold; }
        .warning { background-color: #fff3cd; padding: 10px; border-radius: 5px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Exploit Analysis Report</h1>
        <p><strong>File:</strong> {filename}</p>
        <p><strong>Analysis Date:</strong> {analysis_date}</p>
        <p><strong>Risk Level:</strong> <span class="risk-{risk_class}">{risk_level}</span></p>
    </div>
    
    <div class="warning">
        <strong>⚠️ EDUCATIONAL PURPOSE ONLY ⚠️</strong><br>
        This analysis is for educational and research purposes only.
    </div>
    
    <div class="section">
        <h2>File Information</h2>
        <table>
            <tr><th>Property</th><th>Value</th></tr>
            <tr><td>File Size</td><td>{file_size} bytes</td></tr>
            <tr><td>File Type</td><td>{file_type}</td></tr>
            <tr><td>MD5 Hash</td><td>{md5_hash}</td></tr>
            <tr><td>SHA256 Hash</td><td>{sha256_hash}</td></tr>
        </table>
    </div>
    
    <div class="section">
        <h2>Security Analysis</h2>
        <p><strong>Entropy:</strong> {entropy} ({entropy_assessment})</p>
        <p><strong>Suspicious Patterns:</strong></p>
        <ul>
            {suspicious_patterns}
        </ul>
    </div>
    
    <div class="section">
        <h2>Recommendations</h2>
        <ul>
            {recommendations}
        </ul>
    </div>
</body>
</html>
"""
        
        # Extract data for template
        file_info = analysis_result.get('file_info', {})
        security_analysis = analysis_result.get('security_analysis', {})
        
        risk_level = security_analysis.get('risk_level', 'UNKNOWN')
        risk_class = risk_level.lower()
        
        entropy_info = security_analysis.get('entropy_analysis', {})
        
        suspicious_patterns = security_analysis.get('suspicious_patterns', [])
        patterns_html = ''.join(f'<li>{pattern}</li>' for pattern in suspicious_patterns)
        
        recommendations = analysis_result.get('recommendations', [])
        recommendations_html = ''.join(f'<li>{rec}</li>' for rec in recommendations)
        
        # Fill template
        html_content = html_template.format(
            filename=file_info.get('filename', 'Unknown'),
            analysis_date=time.strftime('%Y-%m-%d %H:%M:%S'),
            risk_level=risk_level,
            risk_class=risk_class,
            file_size=file_info.get('file_size', 0),
            file_type=file_info.get('file_type', 'Unknown'),
            md5_hash=file_info.get('hashes', {}).get('md5', 'N/A'),
            sha256_hash=file_info.get('hashes', {}).get('sha256', 'N/A'),
            entropy=entropy_info.get('entropy', 0),
            entropy_assessment=entropy_info.get('assessment', 'N/A'),
            suspicious_patterns=patterns_html,
            recommendations=recommendations_html
        )
        
        # Save HTML report
        report_filename = f"analysis_report_{int(time.time())}.html"
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return report_filename
    
    def get_analysis_history(self) -> List[Dict]:
        """Get analysis history"""
        return self.analysis_history.copy()
    
    def clear_analysis_history(self):
        """Clear analysis history"""
        self.analysis_history = []
        self.logger.info("Analysis history cleared")

# Example usage
if __name__ == "__main__":
    print("⚠️ Exploit Analyzer - Educational Purpose Only ⚠️")
    
    analyzer = ExploitAnalyzer(debug=True)
    
    # Analyzer would require actual files to analyze
    print("Exploit analyzer initialized")
    print("Use the analyze_file() method to analyze exploit files")
    print("Remember: This is for educational purposes only!")
