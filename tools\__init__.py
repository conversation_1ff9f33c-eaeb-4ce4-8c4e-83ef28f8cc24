"""
Testing and Demonstration Tools Package
Educational tools for safely testing exploit concepts

⚠️ EDUCATIONAL PURPOSE ONLY ⚠️
"""

from .main import main
from .generator.exploit_generator import ExploitGenerator
from .tester.exploit_tester import ExploitTester
from .analyzer.exploit_analyzer import ExploitAnalyzer

__all__ = [
    'main',
    'ExploitGenerator',
    'ExploitTester',
    'ExploitAnalyzer'
]

# Educational disclaimer
DISCLAIMER = """
⚠️  EDUCATIONAL PURPOSE ONLY ⚠️

These tools are for educational and research purposes only.
Using these for malicious purposes is illegal and unethical.
Always follow responsible disclosure practices.
"""

def print_disclaimer():
    """Print the educational disclaimer"""
    print(DISCLAIMER)
