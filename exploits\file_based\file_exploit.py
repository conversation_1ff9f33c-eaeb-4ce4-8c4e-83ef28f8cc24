#!/usr/bin/env python3
"""
File-Based Exploit Simulation
Educational demonstration of how file-based exploits work

⚠️ EDUCATIONAL PURPOSE ONLY ⚠️
"""

import os
import struct
import hashlib
import binascii
from typing import Dict, List, Optional, Tuple
from cryptography.fernet import Fernet
import logging

class FileExploit:
    """
    Simulates file-based exploit techniques for educational purposes
    """
    
    def __init__(self, debug: bool = False):
        """Initialize the file exploit simulator"""
        self.debug = debug
        self.logger = self._setup_logger()
        self.payloads = {}
        self.exploit_templates = {}
        self._load_exploit_templates()
        
        # Educational disclaimer
        self.logger.warning("⚠️ EDUCATIONAL PURPOSE ONLY - DO NOT USE MALICIOUSLY ⚠️")
    
    def _setup_logger(self) -> logging.Logger:
        """Setup logging for the exploit simulator"""
        logger = logging.getLogger('FileExploit')
        logger.setLevel(logging.DEBUG if self.debug else logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _load_exploit_templates(self):
        """Load exploit templates for different file types"""
        self.exploit_templates = {
            'pdf': {
                'header': b'%PDF-1.4\n',
                'vulnerability': 'buffer_overflow_in_parser',
                'trigger_pattern': b'A' * 1024,  # Simulated overflow pattern
                'payload_offset': 512
            },
            'zip': {
                'header': b'PK\x03\x04',
                'vulnerability': 'path_traversal',
                'trigger_pattern': b'../../../',
                'payload_offset': 30
            },
            'docx': {
                'header': b'PK\x03\x04',  # DOCX is ZIP-based
                'vulnerability': 'macro_execution',
                'trigger_pattern': b'<script>',
                'payload_offset': 100
            }
        }
    
    def generate_malicious_pdf(self, output_path: str, payload: bytes = None) -> str:
        """
        Generate a simulated malicious PDF file
        
        Args:
            output_path: Path to save the malicious PDF
            payload: Custom payload to embed (optional)
            
        Returns:
            Path to the generated file
        """
        self.logger.info(f"Generating malicious PDF: {output_path}")
        
        if payload is None:
            payload = self._generate_default_payload()
        
        # Simulated PDF structure with embedded exploit
        pdf_content = self._create_pdf_structure(payload)
        
        with open(output_path, 'wb') as f:
            f.write(pdf_content)
        
        self.logger.info(f"Malicious PDF generated: {output_path}")
        return output_path
    
    def generate_malicious_zip(self, output_path: str, payload_files: Dict[str, bytes] = None) -> str:
        """
        Generate a simulated malicious ZIP file with path traversal
        
        Args:
            output_path: Path to save the malicious ZIP
            payload_files: Dictionary of filename -> content
            
        Returns:
            Path to the generated file
        """
        self.logger.info(f"Generating malicious ZIP: {output_path}")
        
        if payload_files is None:
            payload_files = {
                '../../../evil.exe': self._generate_default_payload(),
                'normal_file.txt': b'This looks normal'
            }
        
        zip_content = self._create_zip_structure(payload_files)
        
        with open(output_path, 'wb') as f:
            f.write(zip_content)
        
        self.logger.info(f"Malicious ZIP generated: {output_path}")
        return output_path
    
    def generate_malicious_docx(self, output_path: str, macro_payload: str = None) -> str:
        """
        Generate a simulated malicious DOCX file with macro
        
        Args:
            output_path: Path to save the malicious DOCX
            macro_payload: VBA macro code to embed
            
        Returns:
            Path to the generated file
        """
        self.logger.info(f"Generating malicious DOCX: {output_path}")
        
        if macro_payload is None:
            macro_payload = self._generate_default_macro()
        
        docx_content = self._create_docx_structure(macro_payload)
        
        with open(output_path, 'wb') as f:
            f.write(docx_content)
        
        self.logger.info(f"Malicious DOCX generated: {output_path}")
        return output_path
    
    def _generate_default_payload(self) -> bytes:
        """Generate a default simulated payload"""
        # This is a simulated payload for educational purposes
        payload = b"""
        # SIMULATED PAYLOAD - EDUCATIONAL ONLY
        # This represents what malicious code might look like
        
        import os
        import subprocess
        
        def execute_payload():
            # Simulated malicious actions
            print("Payload executed (SIMULATION)")
            # In real exploit: system compromise, data theft, etc.
            pass
        
        execute_payload()
        """
        
        # Encrypt the payload to simulate obfuscation
        key = Fernet.generate_key()
        cipher = Fernet(key)
        encrypted_payload = cipher.encrypt(payload)
        
        return encrypted_payload
    
    def _generate_default_macro(self) -> str:
        """Generate a default simulated VBA macro"""
        return """
        ' SIMULATED MALICIOUS MACRO - EDUCATIONAL ONLY
        Sub Auto_Open()
            ' This would execute when document opens
            MsgBox "Payload executed (SIMULATION)"
            
            ' In real exploit:
            ' - Download additional payloads
            ' - Execute system commands
            ' - Steal credentials
            ' - Establish persistence
        End Sub
        """
    
    def _create_pdf_structure(self, payload: bytes) -> bytes:
        """Create a simulated malicious PDF structure"""
        # Simplified PDF structure for demonstration
        pdf_header = b'%PDF-1.4\n'
        
        # Simulated PDF objects with embedded payload
        pdf_objects = f"""
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length {len(payload)}
>>
stream
{payload.decode('latin-1', errors='ignore')}
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000010 00000 n 
0000000079 00000 n 
0000000173 00000 n 
0000000301 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
{400 + len(payload)}
%%EOF
""".encode('latin-1')
        
        return pdf_header + pdf_objects
    
    def _create_zip_structure(self, files: Dict[str, bytes]) -> bytes:
        """Create a simulated malicious ZIP structure"""
        # Simplified ZIP structure for demonstration
        zip_content = b'PK\x03\x04'  # ZIP signature
        
        for filename, content in files.items():
            # Add file entry (simplified)
            filename_bytes = filename.encode('utf-8')
            zip_content += struct.pack('<H', len(filename_bytes))
            zip_content += struct.pack('<L', len(content))
            zip_content += filename_bytes
            zip_content += content
        
        return zip_content
    
    def _create_docx_structure(self, macro: str) -> bytes:
        """Create a simulated malicious DOCX structure"""
        # DOCX is essentially a ZIP file with XML documents
        docx_files = {
            '[Content_Types].xml': b'<?xml version="1.0"?>...',
            'word/document.xml': f'<?xml version="1.0"?><document>{macro}</document>'.encode(),
            'word/vbaProject.bin': macro.encode()
        }
        
        return self._create_zip_structure(docx_files)
    
    def analyze_file(self, file_path: str) -> Dict:
        """
        Analyze a file for potential exploit indicators
        
        Args:
            file_path: Path to the file to analyze
            
        Returns:
            Dictionary with analysis results
        """
        self.logger.info(f"Analyzing file: {file_path}")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        analysis = {
            'file_path': file_path,
            'file_size': os.path.getsize(file_path),
            'file_hash': self._calculate_file_hash(file_path),
            'file_type': self._detect_file_type(file_path),
            'suspicious_patterns': [],
            'exploit_indicators': []
        }
        
        # Read file content for analysis
        with open(file_path, 'rb') as f:
            content = f.read()
        
        # Check for suspicious patterns
        analysis['suspicious_patterns'] = self._find_suspicious_patterns(content)
        analysis['exploit_indicators'] = self._find_exploit_indicators(content)
        
        self.logger.info(f"Analysis complete for: {file_path}")
        return analysis
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA256 hash of file"""
        sha256_hash = hashlib.sha256()
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    
    def _detect_file_type(self, file_path: str) -> str:
        """Detect file type based on magic bytes"""
        with open(file_path, 'rb') as f:
            header = f.read(16)
        
        if header.startswith(b'%PDF'):
            return 'PDF'
        elif header.startswith(b'PK\x03\x04'):
            return 'ZIP/Office'
        elif header.startswith(b'\x89PNG'):
            return 'PNG'
        elif header.startswith(b'\xff\xd8\xff'):
            return 'JPEG'
        else:
            return 'Unknown'
    
    def _find_suspicious_patterns(self, content: bytes) -> List[str]:
        """Find suspicious patterns in file content"""
        patterns = []
        
        # Check for common exploit patterns
        if b'A' * 100 in content:  # Potential buffer overflow
            patterns.append('Potential buffer overflow pattern')
        
        if b'../../../' in content:  # Path traversal
            patterns.append('Path traversal pattern')
        
        if b'<script>' in content:  # Script injection
            patterns.append('Script injection pattern')
        
        return patterns
    
    def _find_exploit_indicators(self, content: bytes) -> List[str]:
        """Find exploit indicators in file content"""
        indicators = []
        
        # Check for shellcode patterns
        if b'\x90\x90\x90\x90' in content:  # NOP sled
            indicators.append('Potential NOP sled detected')
        
        # Check for encrypted payloads
        entropy = self._calculate_entropy(content)
        if entropy > 7.5:  # High entropy suggests encryption/compression
            indicators.append('High entropy content (possible encrypted payload)')
        
        return indicators
    
    def _calculate_entropy(self, data: bytes) -> float:
        """Calculate Shannon entropy of data"""
        if not data:
            return 0

        # Count frequency of each byte
        frequency = {}
        for byte in data:
            frequency[byte] = frequency.get(byte, 0) + 1

        # Calculate entropy
        entropy = 0
        data_len = len(data)
        for count in frequency.values():
            probability = count / data_len
            if probability > 0:
                import math
                entropy -= probability * math.log2(probability)

        return entropy

    def create_steganographic_file(self, cover_file: str, payload: bytes, output_file: str) -> str:
        """
        Create a file with hidden payload using steganography

        Args:
            cover_file: Original file to hide payload in
            payload: Data to hide
            output_file: Output file with hidden payload

        Returns:
            Path to the output file
        """
        self.logger.info(f"Creating steganographic file: {output_file}")

        with open(cover_file, 'rb') as f:
            cover_data = f.read()

        # Simple LSB steganography simulation
        modified_data = self._embed_payload_lsb(cover_data, payload)

        with open(output_file, 'wb') as f:
            f.write(modified_data)

        self.logger.info(f"Steganographic file created: {output_file}")
        return output_file

    def _embed_payload_lsb(self, cover_data: bytes, payload: bytes) -> bytes:
        """Embed payload using LSB steganography (simplified simulation)"""
        # This is a simplified simulation for educational purposes
        modified = bytearray(cover_data)

        # Embed payload length first
        payload_len = len(payload)
        len_bytes = payload_len.to_bytes(4, 'little')

        # Embed in first few bytes (simplified)
        for i, byte in enumerate(len_bytes + payload):
            if i < len(modified):
                # Modify LSB
                modified[i] = (modified[i] & 0xFE) | (byte & 0x01)

        return bytes(modified)

# Example usage and testing
if __name__ == "__main__":
    # Educational demonstration
    print("⚠️ File-Based Exploit Simulation - Educational Purpose Only ⚠️")

    exploit = FileExploit(debug=True)

    # Generate sample malicious files
    exploit.generate_malicious_pdf("sample_malicious.pdf")
    exploit.generate_malicious_zip("sample_malicious.zip")
    exploit.generate_malicious_docx("sample_malicious.docx")

    print("Sample malicious files generated for educational analysis")
    print("Remember: These are simulations for learning purposes only!")
