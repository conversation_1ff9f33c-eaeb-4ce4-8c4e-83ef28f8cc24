#!/usr/bin/env python3
"""
Persistence Manager - Simulation of persistence techniques
Educational demonstration of how malware maintains access

⚠️ EDUCATIONAL PURPOSE ONLY ⚠️
"""

import os
import time
import json
from typing import Dict, List, Optional, Any
import logging

class PersistenceManager:
    """
    Simulates persistence techniques for educational purposes
    """
    
    def __init__(self, debug: bool = False):
        """Initialize the persistence manager"""
        self.debug = debug
        self.logger = self._setup_logger()
        
        # Persistence configuration
        self.config = {
            'registry_persistence_enabled': True,
            'service_persistence_enabled': True,
            'scheduled_task_enabled': True,
            'startup_folder_enabled': True,
            'dll_hijacking_enabled': True,
            'wmi_persistence_enabled': True,
            'backup_persistence_count': 3,  # Number of backup persistence methods
            'persistence_check_interval': 300,  # 5 minutes
        }
        
        # Persistence techniques
        self.persistence_techniques = {
            'registry_run_keys': {
                'description': 'Registry Run keys for startup persistence',
                'detection_difficulty': 'LOW',
                'persistence_level': 'USER',
                'locations': [
                    'HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run',
                    'HKLM\\Software\\Microsoft\\Windows\\CurrentVersion\\Run'
                ]
            },
            'windows_services': {
                'description': 'Windows service for system-level persistence',
                'detection_difficulty': 'MEDIUM',
                'persistence_level': 'SYSTEM',
                'service_types': ['auto_start', 'demand_start', 'delayed_start']
            },
            'scheduled_tasks': {
                'description': 'Scheduled tasks for timed execution',
                'detection_difficulty': 'MEDIUM',
                'persistence_level': 'USER/SYSTEM',
                'trigger_types': ['logon', 'startup', 'idle', 'time_based']
            },
            'startup_folders': {
                'description': 'Startup folder placement',
                'detection_difficulty': 'LOW',
                'persistence_level': 'USER',
                'locations': [
                    '%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Startup',
                    '%ALLUSERSPROFILE%\\Microsoft\\Windows\\Start Menu\\Programs\\Startup'
                ]
            },
            'dll_hijacking': {
                'description': 'DLL search order hijacking',
                'detection_difficulty': 'HIGH',
                'persistence_level': 'APPLICATION',
                'target_applications': ['common_apps', 'system_processes']
            },
            'wmi_persistence': {
                'description': 'WMI event subscription persistence',
                'detection_difficulty': 'HIGH',
                'persistence_level': 'SYSTEM',
                'event_types': ['process_creation', 'file_modification', 'registry_change']
            },
            'com_hijacking': {
                'description': 'COM object hijacking',
                'detection_difficulty': 'HIGH',
                'persistence_level': 'USER/SYSTEM',
                'target_objects': ['common_com_objects']
            }
        }
        
        # Active persistence mechanisms
        self.active_persistence = {}
        self.persistence_history = []
        
        self.logger.warning("⚠️ EDUCATIONAL PURPOSE ONLY - DO NOT USE MALICIOUSLY ⚠️")
    
    def _setup_logger(self) -> logging.Logger:
        """Setup logging for the persistence manager"""
        logger = logging.getLogger('PersistenceManager')
        logger.setLevel(logging.DEBUG if self.debug else logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def establish_persistence(self, techniques: List[str] = None) -> Dict:
        """
        Establish persistence using specified techniques
        
        Args:
            techniques: List of persistence techniques to use
            
        Returns:
            Dictionary with persistence establishment results
        """
        self.logger.info("Establishing persistence mechanisms")
        
        if techniques is None:
            # Use default set of techniques
            techniques = ['registry_run_keys', 'scheduled_tasks', 'startup_folders']
        
        results = {
            'persistence_established': True,
            'successful_techniques': [],
            'failed_techniques': [],
            'establishment_time': time.time(),
            'backup_count': 0
        }
        
        # Establish each persistence technique
        for technique in techniques:
            if technique not in self.persistence_techniques:
                self.logger.warning(f"Unknown persistence technique: {technique}")
                results['failed_techniques'].append(technique)
                continue
            
            try:
                if self._establish_technique(technique):
                    results['successful_techniques'].append(technique)
                    self.active_persistence[technique] = {
                        'established_at': time.time(),
                        'status': 'ACTIVE',
                        'config': self.persistence_techniques[technique].copy(),
                        'check_count': 0,
                        'last_verified': time.time()
                    }
                    results['backup_count'] += 1
                else:
                    results['failed_techniques'].append(technique)
            except Exception as e:
                self.logger.error(f"Failed to establish {technique}: {str(e)}")
                results['failed_techniques'].append(technique)
        
        # Log persistence establishment
        self.persistence_history.append({
            'action': 'ESTABLISHED',
            'techniques': results['successful_techniques'],
            'timestamp': time.time()
        })
        
        self.logger.info(f"Persistence established: {len(results['successful_techniques'])} techniques active")
        return results
    
    def _establish_technique(self, technique: str) -> bool:
        """Establish a specific persistence technique (simulation)"""
        self.logger.debug(f"Establishing persistence technique: {technique}")
        
        if technique == 'registry_run_keys':
            return self._simulate_registry_persistence()
        elif technique == 'windows_services':
            return self._simulate_service_persistence()
        elif technique == 'scheduled_tasks':
            return self._simulate_scheduled_task_persistence()
        elif technique == 'startup_folders':
            return self._simulate_startup_folder_persistence()
        elif technique == 'dll_hijacking':
            return self._simulate_dll_hijacking_persistence()
        elif technique == 'wmi_persistence':
            return self._simulate_wmi_persistence()
        elif technique == 'com_hijacking':
            return self._simulate_com_hijacking_persistence()
        else:
            return False
    
    def _simulate_registry_persistence(self) -> bool:
        """Simulate registry-based persistence"""
        self.logger.debug("Simulating registry persistence")
        
        # Simulate registry key creation
        registry_locations = self.persistence_techniques['registry_run_keys']['locations']
        
        for location in registry_locations:
            self.logger.info(f"Simulating registry entry: {location}\\MalwareEntry (SIMULATION)")
            # In real implementation: would create registry entry
            time.sleep(0.1)
        
        return True
    
    def _simulate_service_persistence(self) -> bool:
        """Simulate Windows service persistence"""
        self.logger.debug("Simulating service persistence")
        
        service_config = {
            'service_name': 'WindowsUpdateService',
            'display_name': 'Windows Update Helper Service',
            'description': 'Provides update functionality for Windows components',
            'start_type': 'auto_start',
            'executable_path': 'C:\\Windows\\System32\\svchost.exe'
        }
        
        self.logger.info(f"Simulating service creation: {service_config['service_name']} (SIMULATION)")
        # In real implementation: would create Windows service
        
        return True
    
    def _simulate_scheduled_task_persistence(self) -> bool:
        """Simulate scheduled task persistence"""
        self.logger.debug("Simulating scheduled task persistence")
        
        task_config = {
            'task_name': 'SystemMaintenanceTask',
            'description': 'Performs routine system maintenance',
            'trigger': 'logon',
            'action': 'C:\\Windows\\System32\\svchost.exe',
            'run_level': 'highest'
        }
        
        self.logger.info(f"Simulating scheduled task: {task_config['task_name']} (SIMULATION)")
        # In real implementation: would create scheduled task
        
        return True
    
    def _simulate_startup_folder_persistence(self) -> bool:
        """Simulate startup folder persistence"""
        self.logger.debug("Simulating startup folder persistence")
        
        startup_locations = self.persistence_techniques['startup_folders']['locations']
        
        for location in startup_locations:
            file_path = f"{location}\\SystemHelper.exe"
            self.logger.info(f"Simulating startup file: {file_path} (SIMULATION)")
            # In real implementation: would copy file to startup folder
            time.sleep(0.1)
        
        return True
    
    def _simulate_dll_hijacking_persistence(self) -> bool:
        """Simulate DLL hijacking persistence"""
        self.logger.debug("Simulating DLL hijacking persistence")
        
        hijack_targets = [
            'C:\\Windows\\System32\\version.dll',
            'C:\\Program Files\\Common Files\\microsoft shared\\ink\\tiptsf.dll'
        ]
        
        for target in hijack_targets:
            self.logger.info(f"Simulating DLL hijacking: {target} (SIMULATION)")
            # In real implementation: would place malicious DLL
            time.sleep(0.1)
        
        return True
    
    def _simulate_wmi_persistence(self) -> bool:
        """Simulate WMI event subscription persistence"""
        self.logger.debug("Simulating WMI persistence")
        
        wmi_config = {
            'event_filter': 'ProcessStartFilter',
            'event_consumer': 'CommandLineEventConsumer',
            'binding': 'ProcessStartBinding',
            'query': "SELECT * FROM Win32_ProcessStartTrace WHERE ProcessName='notepad.exe'"
        }
        
        self.logger.info(f"Simulating WMI event subscription: {wmi_config['event_filter']} (SIMULATION)")
        # In real implementation: would create WMI event subscription
        
        return True
    
    def _simulate_com_hijacking_persistence(self) -> bool:
        """Simulate COM object hijacking persistence"""
        self.logger.debug("Simulating COM hijacking persistence")
        
        com_objects = [
            '{CLSID-1234-5678-9ABC-DEF012345678}',
            '{CLSID-ABCD-EFGH-IJKL-MNOP12345678}'
        ]
        
        for com_object in com_objects:
            self.logger.info(f"Simulating COM hijacking: {com_object} (SIMULATION)")
            # In real implementation: would modify COM registry entries
            time.sleep(0.1)
        
        return True
    
    def verify_persistence(self) -> Dict:
        """
        Verify that persistence mechanisms are still active
        
        Returns:
            Dictionary with verification results
        """
        self.logger.debug("Verifying persistence mechanisms")
        
        verification_result = {
            'verification_time': time.time(),
            'active_techniques': [],
            'failed_techniques': [],
            'repair_needed': [],
            'overall_status': 'UNKNOWN'
        }
        
        # Check each active persistence technique
        for technique, config in self.active_persistence.items():
            try:
                if self._verify_technique(technique):
                    verification_result['active_techniques'].append(technique)
                    config['last_verified'] = time.time()
                    config['check_count'] += 1
                else:
                    verification_result['failed_techniques'].append(technique)
                    verification_result['repair_needed'].append(technique)
                    config['status'] = 'FAILED'
            except Exception as e:
                self.logger.error(f"Failed to verify {technique}: {str(e)}")
                verification_result['failed_techniques'].append(technique)
        
        # Determine overall status
        if len(verification_result['active_techniques']) > 0:
            verification_result['overall_status'] = 'ACTIVE'
        elif len(verification_result['failed_techniques']) > 0:
            verification_result['overall_status'] = 'DEGRADED'
        else:
            verification_result['overall_status'] = 'FAILED'
        
        self.logger.info(f"Persistence verification: {verification_result['overall_status']}")
        return verification_result
    
    def _verify_technique(self, technique: str) -> bool:
        """Verify a specific persistence technique (simulation)"""
        self.logger.debug(f"Verifying persistence technique: {technique}")
        
        # Simulate verification checks
        import random
        
        # Most techniques should verify successfully in simulation
        success_rate = 0.9
        
        if random.random() < success_rate:
            self.logger.debug(f"Persistence technique verified: {technique}")
            return True
        else:
            self.logger.warning(f"Persistence technique failed verification: {technique}")
            return False
    
    def repair_persistence(self, failed_techniques: List[str]) -> Dict:
        """
        Repair failed persistence mechanisms
        
        Args:
            failed_techniques: List of techniques that need repair
            
        Returns:
            Dictionary with repair results
        """
        self.logger.info(f"Repairing persistence mechanisms: {failed_techniques}")
        
        repair_result = {
            'repair_time': time.time(),
            'repaired_techniques': [],
            'failed_repairs': [],
            'new_techniques_added': []
        }
        
        # Attempt to repair each failed technique
        for technique in failed_techniques:
            try:
                if self._establish_technique(technique):
                    repair_result['repaired_techniques'].append(technique)
                    if technique in self.active_persistence:
                        self.active_persistence[technique]['status'] = 'ACTIVE'
                        self.active_persistence[technique]['last_verified'] = time.time()
                else:
                    repair_result['failed_repairs'].append(technique)
            except Exception as e:
                self.logger.error(f"Failed to repair {technique}: {str(e)}")
                repair_result['failed_repairs'].append(technique)
        
        # Add backup techniques if repairs failed
        if len(repair_result['failed_repairs']) > 0:
            backup_techniques = self._get_backup_techniques(repair_result['failed_repairs'])
            for backup in backup_techniques:
                if self._establish_technique(backup):
                    repair_result['new_techniques_added'].append(backup)
                    self.active_persistence[backup] = {
                        'established_at': time.time(),
                        'status': 'ACTIVE',
                        'config': self.persistence_techniques[backup].copy(),
                        'check_count': 0,
                        'last_verified': time.time()
                    }
        
        # Log repair activity
        self.persistence_history.append({
            'action': 'REPAIRED',
            'repaired': repair_result['repaired_techniques'],
            'added': repair_result['new_techniques_added'],
            'timestamp': time.time()
        })
        
        self.logger.info(f"Persistence repair completed: {len(repair_result['repaired_techniques'])} repaired")
        return repair_result
    
    def _get_backup_techniques(self, failed_techniques: List[str]) -> List[str]:
        """Get backup persistence techniques"""
        all_techniques = list(self.persistence_techniques.keys())
        active_techniques = list(self.active_persistence.keys())
        
        # Find techniques not currently active
        available_techniques = [t for t in all_techniques if t not in active_techniques]
        
        # Return up to backup_persistence_count techniques
        return available_techniques[:self.config['backup_persistence_count']]
    
    def remove_persistence(self, techniques: List[str] = None) -> Dict:
        """
        Remove persistence mechanisms
        
        Args:
            techniques: List of techniques to remove (None = all)
            
        Returns:
            Dictionary with removal results
        """
        self.logger.info("Removing persistence mechanisms")
        
        if techniques is None:
            techniques = list(self.active_persistence.keys())
        
        removal_result = {
            'removal_time': time.time(),
            'removed_techniques': [],
            'failed_removals': []
        }
        
        # Remove each specified technique
        for technique in techniques:
            if technique not in self.active_persistence:
                self.logger.warning(f"Technique not active: {technique}")
                continue
            
            try:
                if self._remove_technique(technique):
                    removal_result['removed_techniques'].append(technique)
                    del self.active_persistence[technique]
                else:
                    removal_result['failed_removals'].append(technique)
            except Exception as e:
                self.logger.error(f"Failed to remove {technique}: {str(e)}")
                removal_result['failed_removals'].append(technique)
        
        # Log removal activity
        self.persistence_history.append({
            'action': 'REMOVED',
            'techniques': removal_result['removed_techniques'],
            'timestamp': time.time()
        })
        
        self.logger.info(f"Persistence removal completed: {len(removal_result['removed_techniques'])} removed")
        return removal_result
    
    def _remove_technique(self, technique: str) -> bool:
        """Remove a specific persistence technique (simulation)"""
        self.logger.debug(f"Removing persistence technique: {technique}")
        
        # Simulate removal process
        if technique == 'registry_run_keys':
            self.logger.info("Simulating registry entry removal (SIMULATION)")
        elif technique == 'windows_services':
            self.logger.info("Simulating service removal (SIMULATION)")
        elif technique == 'scheduled_tasks':
            self.logger.info("Simulating scheduled task removal (SIMULATION)")
        # Add removal simulation for other techniques
        
        return True
    
    def get_persistence_status(self) -> Dict:
        """Get current persistence status"""
        return {
            'active_persistence': self.active_persistence.copy(),
            'persistence_history': self.persistence_history.copy(),
            'configuration': self.config.copy(),
            'available_techniques': list(self.persistence_techniques.keys())
        }

# Example usage
if __name__ == "__main__":
    print("⚠️ Persistence Manager Simulation - Educational Purpose Only ⚠️")
    
    persistence = PersistenceManager(debug=True)
    
    # Establish persistence
    result = persistence.establish_persistence()
    print(f"Persistence techniques established: {len(result['successful_techniques'])}")
    
    # Verify persistence
    verification = persistence.verify_persistence()
    print(f"Persistence status: {verification['overall_status']}")
    
    # Simulate repair if needed
    if verification['repair_needed']:
        repair_result = persistence.repair_persistence(verification['repair_needed'])
        print(f"Repaired techniques: {len(repair_result['repaired_techniques'])}")
    
    print("Remember: This is a simulation for learning purposes only!")
